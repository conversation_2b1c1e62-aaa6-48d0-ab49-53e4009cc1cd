/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --border-color: #333333;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.logo-img {
    height: 64px;
    width: auto;
    max-width: 320px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo-img:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 8px 16px;
    border: 1px solid var(--primary-color);
    border-radius: 6px;
}

.nav-link:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
}



.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-image: url('images/wallpaper.jpg'), linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    background-size: cover, cover;
    background-position: center, center;
    background-repeat: no-repeat, no-repeat;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.hero-container {
    width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    z-index: 1;
    min-height: 100vh;
}

.hero-content {
    max-width: 500px;
    text-align: right;
    margin-right: 160px;
    padding: 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-weight: 500;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}



/* Section Styles */
section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Projects Section */
.projects {
    background: var(--bg-secondary);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--bg-tertiary);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.project-card:hover::before {
    transform: scaleX(1);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow);
}

.project-image {
    text-align: center;
    margin-bottom: 0.5rem;
}

.project-image img {
    max-width: 100%;
    height: auto;
}

.project-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-align: center;
}

.project-content p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    text-align: center;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: center;
    justify-content: center;
}

.tech-tag {
    background: var(--bg-primary);
    color: var(--primary-color);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid var(--primary-color);
}

/* About Section */
.about {
    background: #000000;
    position: relative;
    overflow: hidden;
}

#particles-js-about {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.about .container {
    position: relative;
    z-index: 2;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text .section-title {
    text-align: left;
    margin-bottom: 2rem;
}

.about-text p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.stat h3 {
    font-size: 2.5rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.team-illustration {
    width: 300px;
    height: 300px;
    background: var(--bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid var(--primary-color);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.team-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Testimonials Section */
.testimonials {
    background: var(--bg-secondary);
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
    min-height: 300px; /* Fixed minimum height to prevent jumping */
    display: flex;
    align-items: center;
}

.testimonial {
    display: none;
    text-align: center;
    padding: 2rem;
    width: 100%;
    min-height: 260px; /* Fixed height for testimonial content */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.testimonial.active {
    display: flex; /* Changed from block to flex to maintain centering */
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.testimonial-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.testimonial-content p {
    font-size: 1.3rem;
    color: var(--text-secondary);
    font-style: italic;
    margin-bottom: 2rem;
    line-height: 1.8;
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.testimonial-author h4 {
    color: var(--text-primary);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.testimonial-author span {
    color: var(--text-muted);
    font-size: 1rem;
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

/* Responsive adjustments for testimonials */
@media (max-width: 768px) {
    .testimonials-slider {
        min-height: 350px; /* Slightly taller on mobile for longer text */
    }

    .testimonial {
        min-height: 310px;
        padding: 1.5rem;
    }

    .testimonial-content p {
        font-size: 1.1rem;
        line-height: 1.6;
    }
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Contact Section */
.contact {
    background: #000000;
    position: relative;
    overflow: hidden;
}

#particles-js {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact-content {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}



.contact-form {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-color);
    width: 100%;
    max-width: 500px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.contact-form .btn {
    display: block;
    margin: 0 auto;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    transition: border-color 0.3s ease;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.form-group select option[disabled] {
    color: var(--text-muted);
}

.form-group select:invalid {
    color: var(--text-muted);
}

.recaptcha-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.recaptcha-container .g-recaptcha {
    transform: scale(0.9);
    transform-origin: center;
}

#submit-button:disabled,
#submit-button.disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none;
    background: #666 !important;
}

/* Footer */
.footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo-img {
    height: 35px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.footer-logo-img:hover {
    transform: scale(1.05);
}

.footer-logo p {
    color: var(--text-secondary);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    color: var(--text-secondary);
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.footer-social a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-secondary);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-container {
        justify-content: flex-end;
        padding: 0 20px;
    }

    .hero-content {
        margin-right: 0;
        text-align: right;
        max-width: 100%;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .about-stats {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .logo-img {
        height: 32px;
        max-width: 160px;
    }
}
