<?php

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message" . PHP_EOL;
    file_put_contents('contact_debug.log', $log_message, FILE_APPEND | LOCK_EX);
    echo "<!-- DEBUG: $message -->" . PHP_EOL;
}

debug_log("=== Contact form processing started ===");
debug_log("Request method: " . $_SERVER['REQUEST_METHOD']);
debug_log("POST data received: " . print_r($_POST, true));

// Google reCAPTCHA API keys settings
$secretKey  = '6LfXF34rAAAAACSrMv7LWaoYmjRL0CZa0BpMYghT';

// Email settings
$recipientEmail = '<EMAIL>';

$ip = get_client_ip();
debug_log("Client IP detected: $ip");

// IP Address Region
$api_access_key = '?access_key=f618c4c56e52923c02e8bab2117b3623';
debug_log("Attempting to get geo location for IP: $ip");
$geoipjson = json_decode(curl_get_contents("http://api.ipstack.com/" . $ip . $api_access_key));
debug_log("Geo IP response: " . print_r($geoipjson, true));
$country = isset($geoipjson->country_code) ? $geoipjson->country_code : 'Unknown';
$country_name = isset($geoipjson->country_name) ? $geoipjson->country_name : 'Unknown';


// If the form is submitted
$postData = $statusMsg = '';
$status = 'error';

// Check if form was submitted (AJAX submissions won't have 'submit' field)
$isFormSubmitted = !empty($_POST) && ($_SERVER['REQUEST_METHOD'] === 'POST');
debug_log("Form submitted check: " . ($isFormSubmitted ? 'YES' : 'NO'));
debug_log("POST submit field present: " . (isset($_POST['submit']) ? 'YES' : 'NO'));

if ($isFormSubmitted) {
    $postData = $_POST;
    debug_log("Processing form submission...");

    // Validate form input fields
    debug_log("Validating required fields...");
    debug_log("Name: " . (isset($_POST['name']) ? $_POST['name'] : 'NOT SET'));
    debug_log("Email: " . (isset($_POST['email']) ? $_POST['email'] : 'NOT SET'));
    debug_log("Message: " . (isset($_POST['message']) ? $_POST['message'] : 'NOT SET'));

    if (!empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['message'])) {
        debug_log("Required fields validation: PASSED");

        // Validate reCAPTCHA checkbox
        debug_log("reCAPTCHA response: " . (isset($_POST['g-recaptcha-response']) ? $_POST['g-recaptcha-response'] : 'NOT SET'));

        if (isset($_POST['g-recaptcha-response']) && !empty($_POST['g-recaptcha-response'])) {
            debug_log("reCAPTCHA validation: PASSED");

            // Verify the reCAPTCHA API response using cURL (since file_get_contents with HTTPS is disabled)
            $verifyURL = 'https://www.google.com/recaptcha/api/siteverify';
            $postData = array(
                'secret' => $secretKey,
                'response' => $_POST['g-recaptcha-response'],
                'remoteip' => $ip
            );

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $verifyURL);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $verifyResponse = curl_exec($ch);
            $curlError = curl_error($ch);
            curl_close($ch);

            debug_log("reCAPTCHA API response: $verifyResponse");
            if ($curlError) {
                debug_log("cURL Error: $curlError");
            }

            // Decode JSON data of API response
            $responseData = json_decode($verifyResponse, true);
            debug_log("reCAPTCHA decoded response: " . print_r($responseData, true));

            // FIXED: Use array notation instead of object notation and check if responseData is valid
            if ($responseData && isset($responseData['success']) && $responseData['success'] == true) {
                debug_log("reCAPTCHA verification: SUCCESS");
                // Retrieve value from the form input fields
                $name = !empty($_POST['name']) ? $_POST['name'] : '';
                $email = !empty($_POST['email']) ? $_POST['email'] : '';
                $phone = !empty($_POST['phone']) ? $_POST['phone'] : '';
                $department = !empty($_POST['department']) ? $_POST['department'] : '';
                $message = !empty($_POST['message']) ? $_POST['message'] : '';
                $user_ip = $ip . ' from ' . $country_name;

                debug_log("Form data extracted successfully");
                debug_log("Name: $name, Email: $email, Department: $department");

                // Send email notification to the site admin
                $to = $recipientEmail;
                $subject = 'New Contact Form Submitted from Code Dynamics website!';
                $htmlContent = "
                        <h2>Code Dynamics | Contact Form Enquiry Details</h2>
                        <p><b>Name: </b>" . htmlspecialchars($name) . "</p>
                        <p><b>Email: </b>" . htmlspecialchars($email) . "</p>
                        <p><b>Phone No: </b>" . htmlspecialchars($phone) . "</p>
                        <p><b>Department: </b>" . htmlspecialchars($department) . "</p>
                        <p><b>Message: </b>" . nl2br(htmlspecialchars($message)) . "</p>
                        <p><b>Form was submitted from IP Address: </b>" . htmlspecialchars($user_ip) . "</p>
                    ";

                // Always set content-type when sending HTML email
                $headers = "MIME-Version: 1.0" . "\r\n";
                $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
                // More headers - FIXED: Use proper From header format
                $headers .= 'From: Code Dynamics Website <<EMAIL>>' . "\r\n";
                $headers .= 'Reply-To: ' . $email . "\r\n";

                debug_log("Attempting to send email to: $to");
                debug_log("Email subject: $subject");
                debug_log("Email headers: $headers");

                // Send email
                $mailResult = mail($to, $subject, $htmlContent, $headers);

                debug_log("Mail function result: " . ($mailResult ? 'SUCCESS' : 'FAILED'));

                if ($mailResult) {
                    debug_log("Email sent successfully!");
                    $status = 'success';
                    $statusMsg = 'Thank you! Your contact request has been submitted successfully.';
                } else {
                    debug_log("Email sending failed!");
                    $status = 'error';
                    $statusMsg = 'Sorry, there was an error sending your message. Please try again.';
                }
                $postData = '';
            } else {
                debug_log("reCAPTCHA verification: FAILED");
                if ($responseData && isset($responseData['error-codes'])) {
                    debug_log("reCAPTCHA error codes: " . implode(', ', $responseData['error-codes']));
                }
                $statusMsg = 'Robot verification failed, please try again.';
            }
        } else {
            debug_log("reCAPTCHA validation: FAILED - No response provided");
            $statusMsg = 'Please check the reCAPTCHA checkbox.';
        }
    } else {
        debug_log("Required fields validation: FAILED");
        $statusMsg = 'Please fill all the mandatory fields.';
    }
} else {
    debug_log("Form not submitted or invalid request method");
}

debug_log("Final status: $status");
debug_log("Final message: $statusMsg");
debug_log("=== Contact form processing completed ===");

function curl_get_contents($url)
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function get_client_ip()
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    }

    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Could be a comma-separated list — take the first one
        $ipList = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ipList[0]);
    }

    if (!empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
        return $_SERVER['HTTP_CF_CONNECTING_IP'];
    }

    return $_SERVER['REMOTE_ADDR'];
}

?>

<?php if (!empty($statusMsg)) { ?>
    <p class="status-msg <?php echo $status; ?>"><?php echo $statusMsg; ?></p>
    <p class="status-msg <?php echo $ip_address_region; ?>"><?php echo "IP Address: ", $user_ip; ?></p>
<?php } ?>