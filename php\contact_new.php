<?php

// Google reCAPTCHA API keys settings
$secretKey  = '6LfXF34rAAAAACSrMv7LWaoYmjRL0CZa0BpMYghT';

// Email settings
$recipientEmail = '<EMAIL>';

$ip = get_client_ip();

// IP Address Region
$api_access_key = '?access_key=f618c4c56e52923c02e8bab2117b3623';
$geoipjson = json_decode(curl_get_contents("http://api.ipstack.com/" . $ip . $api_access_key));
$country = $geoipjson->country_code;
$country_name = $geoipjson->country_name;


// If the form is submitted
$postData = $statusMsg = '';
$status = 'error';
if (isset($_POST['submit'])) {
    $postData = $_POST;

    // Validate form input fields
    if (!empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['message'])) {

        // Validate reCAPTCHA checkbox
        if (isset($_POST['g-recaptcha-response']) && !empty($_POST['g-recaptcha-response'])) {

            // Verify the reCAPTCHA API response
            $verifyResponse = file_get_contents('https://www.google.com/recaptcha/api/siteverify?secret=' . $secretKey . '&response=' . $_POST['g-recaptcha-response']);

            // Decode JSON data of API response
            $responseData = json_decode($verifyResponse, true);

            //check IP region

            if ($responseData->success == true) {
                // Retrieve value from the form input fields
                $name = !empty($_POST['name']) ? $_POST['name'] : '';
                $email = !empty($_POST['email']) ? $_POST['email'] : '';
                $phone = !empty($_POST['phone']) ? $_POST['phone'] : '';
                $department = !empty($_POST['department']) ? $_POST['department'] : '';
                $message = !empty($_POST['message']) ? $_POST['message'] : '';
                $user_ip = $ip . ' from ' . $country_name;

                // Send email notification to the site admin
                $to = $recipientEmail;
                $subject = 'New Contact Form Submitted from Code Dynamics website!';
                $htmlContent = "
                        <h2>Code Dynamics | Contact Form Enquiry Details</h2>
                        <p><b>Name: </b>" . $name . "</p>
                        <p><b>Email: </b>" . $email . "</p>
                        <p><b>Phone No: </b>" . $phone . "</p>
                        <p><b>Department: </b>" . $department . "</p>
                        <p><b>Message: </b>" . $message . "</p>
                        <p><b>Form was submitted from IP Address: </b>" . $user_ip . "</p>
                    ";

                // Always set content-type when sending HTML email
                $headers = "MIME-Version: 1.0" . "\r\n";
                $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
                // More headers
                $headers .= 'From:' . $name . ' <' . $email . '>' . "\r\n";

                // Send email
                mail($to, $subject, $htmlContent, $headers);

                $status = 'success';
                $statusMsg = 'Thank you! Your contact request has been submitted successfully.';
                $postData = '';
            } else {
                $statusMsg = 'Robot verification failed, please try again.';
            }
        } else {
            $statusMsg = 'Please check the reCAPTCHA checkbox.';
        }
    } else {
        $statusMsg = 'Please fill all the mandatory fields.';
    }
}

function curl_get_contents($url)
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function get_client_ip()
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    }

    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Could be a comma-separated list — take the first one
        $ipList = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ipList[0]);
    }

    if (!empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
        return $_SERVER['HTTP_CF_CONNECTING_IP'];
    }

    return $_SERVER['REMOTE_ADDR'];
}

?>

<?php if (!empty($statusMsg)) { ?>
    <p class="status-msg <?php echo $status; ?>"><?php echo $statusMsg; ?></p>
    <p class="status-msg <?php echo $ip_address_region; ?>"><?php echo "IP Address: ", $user_ip; ?></p>
<?php } ?>